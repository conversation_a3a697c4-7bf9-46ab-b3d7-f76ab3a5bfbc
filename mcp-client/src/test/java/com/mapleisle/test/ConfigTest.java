package com.mapleisle.test;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.StopWatch;

/**
 * @ClassName ConfigTest
 * @Description: 测试配置是否正确加载
 * <AUTHOR>
 * @Date 2025/8/18
 * @Version V1.0
 **/
@SpringBootTest
public class ConfigTest {
    @Test
    public void testTime() throws InterruptedException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("任务 1");
        Thread.sleep(1000);
        stopWatch.stop();

        stopWatch.start("任务 2");
        Thread.sleep(700);
        stopWatch.stop();

        stopWatch.start("任务 3");
        Thread.sleep(300);
        stopWatch.stop();

        System.out.println(stopWatch.prettyPrint());
        System.out.println(stopWatch.shortSummary());
    }
}
