server:
  port: 7070
spring:
  application:
    name: springai-mcp-client
  profiles:
    active: dev
  ai:
    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: ${OPENAI_BASE_URL}
      chat:
        options:
          model: ${OPENAI_MODEL}
          temperature: 0.7
          max-tokens: 1000


logging:
  level:
    root: info
    org.springframework.ai: debug
    org.springframework.web.client: debug


