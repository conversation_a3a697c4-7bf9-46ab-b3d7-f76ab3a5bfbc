package com.mapleisle.utils;


import com.mapleisle.enums.SseMsgType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * @ClassName SSEServer
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/8/21 14:45
 * @Version V1.0
 **/
@Slf4j
public class SSEServer {

    // 存放所有用户的 SSE 连接
    private static final Map<String, SseEmitter> sseClients = new ConcurrentHashMap<>();


    /**
     * 功能描述：
     *
     * @param userId 用户ID
     * @return {@link SseEmitter }
     * <AUTHOR>
     * @date 2025-08-21 15:22:32
     */
    public static SseEmitter connect(String userId){
        // 设置超时时间
        SseEmitter sseEmitter = new SseEmitter(0L);
        // 注册回到方法
        sseEmitter.onTimeout(timeoutCallback(userId));
        sseEmitter.onCompletion(completionCallback(userId));
        sseEmitter.onError(errorCallback(userId));
        sseClients.put(userId, sseEmitter);
        log.info("SSE connect success, userId:{}", userId);
        return sseEmitter;
    }

    /**
     * 功能描述：
     *
     * @param userId  用户ID
     * @param message 消息
     * @param msgType msg类型
     * <AUTHOR>
     * @date 2025-08-25 14:11:56
     */
    public static void sendMsg(String userId, String message, SseMsgType msgType){
        if(CollectionUtils.isEmpty(sseClients)){
            return;
        }
        if(sseClients.containsKey(userId)){
            sendEmitterMessage(sseClients.get(userId), message, userId, msgType);
        }
    }

    private static void sendEmitterMessage(SseEmitter sseEmitter, String message, String userId,
                                          SseMsgType msgType){

        try {
            SseEmitter.SseEventBuilder msgEvent = SseEmitter.event()
                    .id(userId)
                    .data(message)
                    .name(msgType.value);
            sseEmitter.send(msgEvent);
        } catch (IOException e) {
           log.error("SSE send message error, userId:{}, message:{}", userId, e.getMessage());
           remove(userId);
        }
    }


    /**
     * 功能描述：sse连接超时回调
     *
     * @param userId 用户ID
     * @return {@link Runnable }
     * <AUTHOR>
     * @date 2025-08-21 15:25:26
     */
    public static Runnable timeoutCallback(String userId){
        return () -> {
            log.info("SSE {} timeout", userId);
            remove(userId);
        };
    }

    public static void remove(String userId) {
        // 删除用户
        sseClients.remove(userId);
        log.info("SSE {} disconnected", userId);
    }

    /**
     * 功能描述：sse连接完成回调
     *
     * @param userId 用户ID
     * @return {@link Runnable }
     * <AUTHOR>
     * @date 2025-08-21 15:37:52
     */
    public static Runnable completionCallback(String userId){
        return () -> {
            log.info("SSE {} completion", userId);
            remove(userId);
        };
    }

    /**
     * 功能描述：sse错误回调
     *
     * @param userId 用户ID
     * @return {@link Consumer }<{@link Throwable }>
     * <AUTHOR>
     * @date 2025-08-21 15:38:02
     */
    public static Consumer<Throwable> errorCallback(String userId){
        return throwable -> {
            log.info("SSE {} error", userId);
            remove(userId);
        };
    }
}
