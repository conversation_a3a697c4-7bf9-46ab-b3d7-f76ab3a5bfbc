package com.mapleisle.service;

import org.springframework.ai.chat.model.ChatResponse;
import reactor.core.publisher.Flux;

/**
 * @ClassName ChatService
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/8/18 17:10
 * @Version V1.0
 **/
public interface ChatService {

    /**
     * 功能描述：测试大模型交互聊天
     *
     * @param prompt 提示词
     * @return {@link String }
     * <AUTHOR>
     * @date 2025-08-18 17:16:13
     */
    public String chatTest(String prompt);

    /**
     * 功能描述：
     *
     * @param msg 消息
     * @return {@link String }
     * <AUTHOR>
     * @date 2025-08-20 17:28:59
     */
    Flux<ChatResponse> charStream(String msg);

    Flux<String> charStr(String msg);
}
