package com.mapleisle.controller;

import com.mapleisle.service.ChatService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@RestController
@RequestMapping("/hello")
@Slf4j
public class HelloController {

    @Resource
    private  ChatService chatService;
    
    /**
     * @MethodName: hello
     * @Description: TODO
     * @Param:
     * @Return: java.lang.String
     * @Author: mapleisle
     * @Date: 2025/8/18
    **/
    @GetMapping("/world")
    public String hello(){
        return "枫知屿";
    }

    @GetMapping("/chat")
    public String chat(String msg){
        log.info("Received chat request with message: {}", msg);
        try {
            if (msg == null || msg.trim().isEmpty()) {
                msg = "Hello, how are you?";
            }
            String response = chatService.chatTest(msg);
            log.info("Chat response generated successfully");
            return response;
        } catch (Exception e) {
            log.error("Error in chat endpoint", e);
            return "Error: " + e.getMessage();
        }
    }

    /**
     * 功能描述：
     *
     * @param msg 消息
     * @return {@link Flux }<{@link ChatResponse }>
     * <AUTHOR>
     * @date 2025-08-20 17:31:53
     */
    @GetMapping("/stream")
    public Flux<ChatResponse> charStream(String msg){
        return chatService.charStream(msg);
    }

    @GetMapping("/stream/str")
    public Flux<String> charStr(String msg, HttpServletResponse response){
        response.setCharacterEncoding("UTF-8");
        return chatService.charStr(msg);
    }


}
