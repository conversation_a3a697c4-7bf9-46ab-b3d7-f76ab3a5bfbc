package com.mapleisle.controller;

import com.mapleisle.utils.SSEServer;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * @ClassName SSEController
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/8/22 14:53
 * @Version V1.0
 **/
@RestController
@RequestMapping("/sse")
public class SSEController {

    @GetMapping(path = "/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter connect(@RequestParam String userId){
        return SSEServer.connect(userId);
    }
}
