package com.mapleisle.controller;

import com.mapleisle.enums.SseMsgType;
import com.mapleisle.utils.SSEServer;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * @ClassName SSEController
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/8/22 14:53
 * @Version V1.0
 **/
@RestController
@RequestMapping("/sse")
public class SSEController {

    @GetMapping(path = "/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter connect(@RequestParam String userId){
        return SSEServer.connect(userId);
    }

    /**
     * 功能描述：sse发送单个消息
     *
     * @param userId  用户ID
     * @param message 消息
     * @return {@link Object }
     * <AUTHOR>
     * @date 2025-08-25 14:32:16
     */
    @GetMapping("/sendMessage")
    public Object sendMessage(@RequestParam String userId, @RequestParam String message){
        SSEServer.sendMsg(userId, message, SseMsgType.MESSAGE);
        return "ok";
    }

}
