package com.mapleisle.aspect;


import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * @ClassName ServiceTimeAspect
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/8/20 17:52
 * @Version V1.0
 **/
@Component
@Slf4j
@Aspect
public class ServiceTimeAspect {

    /**
     * 功能描述： 记录服务耗时
     *          第一个 * 表示任意返回值类型
     *          com.mapleisle.service.impl 指定要切入的class类所在的包名
     *          .. 表示可以匹配到当前包+子包中的类
     *          第二个 * 表示匹配当前包+子包中的类
     *          . 无意义
     *          第三个 * 表示匹配类中的任意方法
     *          (..) 表示匹配任意参数
     * @param joinPoint 连接点
     * @return {@link Object }
     * <AUTHOR>
     * @date 2025-08-20 17:54:44
     */
    @Around("execution(* com.mapleisle.service.impl..*.*(..))")
    public Object recordTimeAspect(ProceedingJoinPoint joinPoint) throws Throwable {
        Long begin = System.currentTimeMillis();
        // 执行目标方法
        Object proceed = joinPoint.proceed();
        String point = joinPoint.getTarget().getClass().getName() + "." + joinPoint.getSignature().getName();
        Long end = System.currentTimeMillis();
        Long takeTime = end - begin;
        // 记录耗时
        if (takeTime > 3000) {
            log.info("Service {} long take {}ms", point, takeTime);
        } else if (takeTime > 2000) {
            log.warn("Service {} middle take {}ms", point, takeTime);
        } else {
            log.error("Service {} take {}ms", point, takeTime);
        }
        // 返回目标方法的返回值
        return proceed;
    }
}
