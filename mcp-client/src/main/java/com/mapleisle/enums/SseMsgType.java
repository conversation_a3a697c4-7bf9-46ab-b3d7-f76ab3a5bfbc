package com.mapleisle.enums;

/**
 * @ClassName SseMsgType
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/8/22 15:07
 * @Version V1.0
 **/
public enum SseMsgType {

    MESSAGE("message", "单词发送的普通消息"),
    ADD("add", "消息追加"),
    FINISH("finish", ""),
    CUSTOM_EVENT("custom_event", "自定义事件"),
    DONE("done", "")
    ;

    private final String type;
    private final String value;

    SseMsgType(String type, String value) {
        this.type = type;
        this.value = value;
    }
}
